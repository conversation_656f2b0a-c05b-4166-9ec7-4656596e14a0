# O2DESNet 3.7.1 - Discrete Event Simulation Engine
## Object-Oriented Discrete Event Simulation Framework

### 🎯 **Overview**

O2DESNet is a comprehensive C# framework for object-oriented discrete event simulation that forms the core simulation engine for the WSC 2024 Port Container Terminal Challenge. It provides a hybrid approach combining both event-based and state-based formalism implemented in an object-oriented programming paradigm.

**Official Description**: "A C# framework for object-oriented discrete event simulator"

**Project Topics**: `simulator`, `simulation`, `discrete-event-simulation`

---

## 📊 **Project Integration**

### **In WSC SimChallenge 2024 Project**
```xml
<PackageReference Include="O2DESNet" Version="3.7.1" />
```

O2DESNet is included as a NuGet package and serves as the **foundational simulation engine** for the entire port simulation system.

### **Repository Information**
- **Official Repository**: [https://github.com/li-haobin/O2DESNet](https://github.com/li-haobin/O2DESNet)
- **Official Website**: [www.o2des.net](http://www.o2des.net)
- **Development Activity**: 377 commits showing continuous development
- **Community**: 25 stars, 8 forks, 6 watchers
- **License**: MIT License (Open Source)
- **Repository Structure**: 
  - Main O2DESNet framework
  - Comprehensive unit test suite
  - Solution and project files
  - Documentation and examples

### **Usage Throughout the Codebase**
- **PortSimModel.cs**: Main simulation model inheriting from `Sandbox`
- **All Entity Classes**: Vessels, Berths, QCs, AGVs, YCs, Containers
- **Activity Classes**: All activities inherit from `BaseActivity<T> : Sandbox`
- **Event Scheduling**: Discrete event management and time progression

---

## 🏗️ **Architecture & Design Philosophy**

### **Hybrid Formalism**
O2DESNet combines two simulation paradigms:

#### **1. Event-Based Formalism (Kernel)**
- **Precise System Modeling**: Accurate representation of system structure and behaviors
- **Event Scheduling**: Discrete events with precise timing control
- **Future Event List**: Efficient event management with proper sequencing

#### **2. State-Based Formalism (Modular Layer)**
- **Hierarchical Modeling**: Nested component structures
- **Modularization**: Reusable simulation components
- **State Management**: Component state transitions and tracking

### **Object-Oriented Paradigm**
- **Abstraction**: Model definitions independent of fidelity levels
- **Encapsulation**: Component-based architecture
- **Inheritance**: Extensible base classes for entities and activities
- **Polymorphism**: Flexible behavior implementation

---

## 🧩 **Core Components**

### **1. Sandbox Architecture**
The fundamental building block of O2DESNet is the `Sandbox` class:

```csharp
// Main simulation model
class PortSimModel : Sandbox
{
    // Simulation logic and components
}

// Activity base class
public class BaseActivity<T> : Sandbox
{
    // Activity-specific behavior
}
```

#### **Key Features:**
- **Simulation Clock**: Unified time management across all components
- **Event Scheduling**: `Schedule()` method for future event planning
- **Hierarchical Nesting**: Sandboxes can contain other sandboxes
- **Component Synchronization**: Shared event list and clock time

### **2. Entity Framework**
O2DESNet entities in the WSC project:

```csharp
// Examples from the WSC project
public class Vessel : Sandbox
{
    public class Waiting : BaseActivity<Vessel>
    public class Berthing : BaseActivity<Vessel>
}

public class AGV : Sandbox
{
    public class BeingIdle : BaseActivity<AGV>
    public class Picking : BaseActivity<AGV>
    public class DeliveringtoYard : BaseActivity<AGV>
}
```

#### **Entity Characteristics:**
- **State Management**: Each entity maintains its current state
- **Activity Composition**: Entities contain multiple activity classes
- **Dynamic Behavior**: State transitions based on simulation events

### **3. Activity System**
Activities represent the behavioral aspects of simulation entities:

```csharp
public class Loading : BaseActivity<QC>
{
    // Activity-specific logic
    // Duration, conditions, transitions
}
```

#### **Activity Features:**
- **Duration Management**: Configurable activity durations
- **Condition Checking**: Pre-conditions and post-conditions
- **Resource Consumption**: Resource allocation and release
- **Event Triggering**: Generate events upon completion

---

## 📈 **Technical Specifications**

### **Platform Support**
- **Primary**: .NET Standard 2.0 (Recommended)
- **Minimum**: .NET Framework 4.7.2
- **Current Project**: .NET 8.0 (Forward compatible)

### **Cross-Platform Compatibility**
- **Windows**: Full support with Visual Studio
- **Linux**: .NET Core 2.2/3.0+ support
- **macOS**: .NET Core 2.2/3.0+ support

### **Setup Options (from GitHub)**
#### **For .NET Core 2.2/3.0 (Windows/Linux/macOS)**
- **Option 1**: Install .NET Core SDK
- **Option 2**: From Visual Studio Installer, enable Cross-Platform Development

#### **For .NET Framework 4.7.2 (Windows Only)**
- **Option 1**: Install .NET Framework 4.7.2
- **Option 2**: From Visual Studio Installer, install .NET Framework 4.7.2 component

### **Performance Characteristics**
- **Event Scheduling**: O(log n) complexity for future event list
- **Memory Efficient**: Minimal overhead per simulation component
- **Scalability**: Supports large-scale simulations with thousands of entities

---

## 🔧 **Key Features**

### **1. HourCounter Statistics**
Advanced statistical collection system:
```csharp
// Usage in WSC project for resource utilization tracking
HourCounter berthUtilization = new HourCounter();
HourCounter agvWorkload = new HourCounter();
```

#### **Capabilities:**
- **Real-time Statistics**: Continuous data collection during simulation
- **Utilization Tracking**: Resource efficiency measurement
- **Performance Metrics**: Comprehensive system performance analysis
- **Warm-up Support**: Exclude initial transient periods

### **2. Future Event List (FEL)**
Sophisticated event management:
- **Unique Event Indexing**: All events are uniquely indexed
- **Deterministic Ordering**: Same-time events ordered by generation sequence
- **Reproducible Results**: Single random seed ensures consistent outcomes

### **3. Resource Management**
Built-in resource handling:
- **Resource Allocation**: Automatic resource assignment and release
- **Constraint Handling**: Resource capacity limits and queuing
- **Utilization Monitoring**: Real-time resource usage statistics

### **4. Random Number Generation**
Comprehensive probability distributions:
- **Standard Distributions**: Uniform, Exponential, Normal, Gamma
- **Advanced Distributions**: Beta, LogNormal, Triangular
- **Custom Empirical**: User-defined probability distributions
- **Seed Management**: Reproducible random sequences

---

## 🚀 **WSC Project Implementation**

### **Simulation Architecture**
```
PortSimModel (Sandbox)
├── Vessels (Multiple Sandbox instances)
│   ├── Waiting Activity
│   └── Berthing Activity
├── Berths (Multiple Sandbox instances)
│   ├── BeingIdle Activity
│   └── BeingOccupied Activity
├── QCs (Multiple Sandbox instances)
│   ├── Discharging Activity
│   └── Loading Activity
├── AGVs (Multiple Sandbox instances)
│   ├── Picking Activity
│   └── Delivering Activity
└── YCs (Multiple Sandbox instances)
    ├── Stacking Activity
    └── Unstacking Activity
```

### **Event Flow Example**
```csharp
// Vessel arrival triggers berth allocation
vessel.Schedule(new VesselArrival(), arrivalTime);

// Berth allocation triggers QC assignment
berth.Schedule(new BerthAllocation(), DateTime.Now);

// QC operation triggers AGV dispatch
qc.Schedule(new ContainerDischarge(), serviceTime);

// AGV transport triggers yard operations
agv.Schedule(new ContainerTransport(), transportTime);
```

### **Integration with Decision Making**
```csharp
// O2DESNet provides the simulation framework
// DecisionMaker provides the optimization logic
public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
{
    // O2DESNet manages the simulation state
    // Decision logic optimizes the allocation
    return optimizedBerth;
}
```

---

## 🛠️ **Development Tools & Extensions**

### **O2DESNet Ecosystem**
- **O2DESNet Core**: Main simulation framework ([GitHub](https://github.com/li-haobin/O2DESNet))
- **O2DESNet.UnitTests**: Comprehensive unit testing suite (included in main repo)
- **O2DESNet.RCQueues**: Resource-constrained queueing systems ([GitHub](https://github.com/li-haobin/O2DESNet.RCQueues))
- **O2DESpy**: Python version of the framework ([GitHub](https://github.com/steveyeoks/o2despy))
- **O2DESNetDemos**: Example implementations and tutorials ([GitHub](https://github.com/li-haobin/O2DESNetDemos))

### **Debug and Analysis Features**
Available debug modes in WSC project:
```csharp
#define DebugofBerth     // Berth utilization details
#define DebugofVessel    // Vessel movement tracking
#define DebugofQC        // Quay crane operations
#define DebugofAGV       // AGV movement and assignments
#define DebugofYC        // Yard crane activities
```

### **Statistical Analysis**
Built-in analytical capabilities:
- **Utilization Rates**: Resource efficiency measurements
- **Throughput Analysis**: System performance metrics
- **Waiting Time Statistics**: Queue performance indicators
- **Bottleneck Identification**: System constraint analysis

---

## 📚 **Version History & Evolution**

### **Version 3.7.1 (Current)**
- **Enhanced HourCounter**: Improved statistical synchronization
- **Performance Optimization**: Better memory usage and speed
- **Bug Fixes**: Resolved FEL ordering issues
- **Compatibility**: Full .NET 8.0 support

### **Detailed Version History (from GitHub)**

#### **Version 3.6**
- **Improvement of HourCounter**: Enhanced synchronization with simulator ClockTime

#### **Version 3.5.1**
- **Added Distributions**: Beta, LogNormal, Normal, Triangular distributions

#### **Version 2.4.1**
- **Random Generators**: Include Uniform and Exponential random generators

#### **Version 2.4**
- **PathMover Module**: Dynamic server-based PathMover for traffic network simulation
- **Custom Random Generators**: Empirical and Gamma with parameter fitting

#### **Version 2.3.2**
- **[Critical Fix]**: Unique event indexing - prevents untraceable simulation distortions
- **FEL Ordering**: Revised sorting mechanism ensuring same-time events ordered by generation sequence
- **Reproducibility**: Single random seed guarantees identical sample paths

#### **Version 2.3.1**
- **HourCounter Enhancement**: Added Histogram and Percentile methods for distribution statistics
- **Server Statistics**: Differentiated "Utilization" vs "Occupation" metrics

#### **Version 2.3.0**
- **Modular Revision**: Higher modularity for Queue and Server, DEVS compliance
- **New Components**: Added Synchronizer for multi-condition event triggering
- **Terminology**: Renamed "Status" to "State" for literature consistency

#### **Version 2.2.1**
- **Visualization**: Enhanced Path label font-size for dense networks
- **SVG Support**: Added X, Y, Rotate transform to Path SVG description

#### **Version 2.2.0**
- **Server Enhancement**: Improved Depart() event handling in Server class
- **FIFO Server**: New implementation for PathMover system modeling
- **Dynamic Properties**: Added NCoccupied property for easy resource tracking
- **PathMover**: Integrated advanced path-based movement system

#### **Version 2.1.2**
- **Load Properties**: Added Static properties as "Category" for Load objects
- **Component Structure**: Renamed Static property to "Config" for normal Components

#### **Version 2.1.1**
- **Simplified Structure**: Encapsulated StaticProperty based on root Component class

#### **Version 2.1.0**
- **Simulator Construction**: Build Simulator by Assembly (Component)
- **Architecture Simplification**: Removed TScenario and TStatus paradigms

---

## 🔄 **Comparison with Other Simulation Frameworks**

### **O2DESNet vs. Traditional DES Tools**

| Feature | O2DESNet | Traditional Tools |
|---------|----------|-------------------|
| **Paradigm** | Object-Oriented + Event-Based | Process-Oriented |
| **Modularity** | High (Sandbox nesting) | Medium |
| **Extensibility** | Excellent (C# inheritance) | Limited |
| **Performance** | High | Variable |
| **Learning Curve** | Moderate | Steep |
| **Integration** | Excellent (.NET ecosystem) | Limited |

### **Advantages of O2DESNet**
- **Modern Architecture**: Object-oriented design principles
- **Flexibility**: Hybrid event/state-based approach
- **Scalability**: Efficient for large-scale simulations
- **Integration**: Seamless .NET ecosystem compatibility
- **Maintainability**: Clean code architecture and modularity

---

## 📖 **Learning Resources**

### **Documentation**
- **Official Repository**: [GitHub - O2DESNet](https://github.com/li-haobin/O2DESNet)
- **Official Website**: [www.o2des.net](http://www.o2des.net)
- **Demo Examples**: [O2DESNetDemos](https://github.com/li-haobin/O2DESNetDemos)
- **Academic Papers**: Multiple publications on O2DES methodology

### **Example Applications**
- **Port Terminal Simulation**: WSC 2024 Challenge (this project)
- **Manufacturing Systems**: Production line optimization
- **Healthcare Systems**: Hospital workflow simulation
- **Traffic Networks**: Urban transportation modeling

### **Tutorial Examples**
1. **Hello World**: Basic event scheduling
2. **M/M/c Queue**: Queueing system simulation
3. **Manufacturing Line**: Multi-stage production process
4. **Hospital Emergency**: Healthcare resource management

---

## 🎯 **Best Practices**

### **Model Design**
- **Hierarchical Structure**: Use nested Sandboxes for complex systems
- **Activity Decomposition**: Break complex behaviors into discrete activities
- **State Management**: Clearly define entity states and transitions
- **Event Scheduling**: Use appropriate time delays and event sequencing

### **Performance Optimization**
- **Efficient Data Structures**: Use appropriate collections for entity management
- **Memory Management**: Dispose of unused simulation objects
- **Statistical Collection**: Balance detail level with performance needs
- **Random Number Management**: Use appropriate random streams

### **Debugging and Validation**
- **Incremental Development**: Build and test components incrementally
- **Statistical Validation**: Compare results with analytical models
- **Sensitivity Analysis**: Test model robustness to parameter changes
- **Reproducibility**: Ensure consistent results with fixed random seeds

### **Testing and Quality Assurance**
- **Unit Test Suite**: Comprehensive O2DESNet.UnitTests included in repository
- **Continuous Integration**: GitHub-based development workflow
- **Code Quality**: 377 commits show rigorous development process
- **Community Testing**: Open source allows community validation

---

## 🌐 **Community & Support**

### **Development Team**
- **Lead Developer**: Li Haobin
- **Institution**: National University of Singapore
- **License**: MIT License
- **GitHub Repository**: [https://github.com/li-haobin/O2DESNet](https://github.com/li-haobin/O2DESNet)
- **Repository Statistics**: 25 stars, 8 forks, 377 commits, 6 watchers
- **Development Activity**: Active development with regular commits and updates

### **Academic Backing**
- **Research Foundation**: Based on extensive academic research
- **Publication History**: Multiple peer-reviewed papers
- **University Usage**: Adopted by educational institutions
- **Industry Applications**: Used in commercial simulation projects

### **Contributing**
- **Open Source**: MIT License allows modification and redistribution
- **Issue Tracking**: GitHub issue system for bug reports and feature requests ([Current Issues](https://github.com/li-haobin/O2DESNet/issues))
- **Pull Requests**: Community contributions welcome ([Current PRs](https://github.com/li-haobin/O2DESNet/pulls))
- **Documentation**: Collaborative documentation improvement
- **Active Development**: 377 commits show ongoing maintenance and improvement
- **Community Engagement**: 25 stars, 8 forks demonstrate active user base

---

## 🔮 **Future Directions**

### **Planned Enhancements**
- **Machine Learning Integration**: AI-driven simulation optimization
- **Cloud Computing**: Distributed simulation capabilities
- **Real-time Simulation**: Enhanced performance for real-time applications
- **Visualization**: Improved graphical simulation representation

### **Research Areas**
- **Adaptive Simulation**: Dynamic model adaptation during runtime
- **Multi-fidelity Modeling**: Integration of different detail levels
- **Uncertainty Quantification**: Advanced statistical analysis
- **Parallel Processing**: Multi-core simulation execution

---

## 📋 **Conclusion**

O2DESNet 3.7.1 represents a mature, sophisticated discrete event simulation framework that successfully bridges the gap between academic research and practical industrial applications. Its object-oriented architecture, combined with hybrid event/state-based formalism, provides a powerful foundation for complex system modeling.

In the WSC 2024 Port Container Terminal Challenge, O2DESNet demonstrates its capability to handle:
- **Complex System Modeling**: Multiple interacting entities and resources
- **Real-time Decision Making**: Integration with optimization algorithms
- **Performance Analysis**: Comprehensive statistical collection
- **Scalability**: Efficient simulation of large-scale operations

The framework's strength lies in its balance of:
- **Theoretical Rigor**: Sound simulation methodology
- **Practical Usability**: Intuitive object-oriented design
- **Performance**: Efficient execution for large simulations
- **Extensibility**: Easy integration with custom logic and optimization

**O2DESNet continues to evolve as a leading discrete event simulation framework, providing researchers and practitioners with a powerful tool for system analysis and optimization.**

### **Current Status & Maintenance**
- **Active Development**: 377 commits demonstrate continuous improvement and maintenance
- **Community Support**: 25 stars, 8 forks, 6 watchers show healthy community engagement
- **Quality Assurance**: Comprehensive unit test suite ensures reliability
- **Open Source**: MIT License enables free use and modification
- **Professional Support**: Backed by academic institution (National University of Singapore)

---

**Framework**: O2DESNet 3.7.1  
**Project**: WSC 2024 Simulation Challenge  
**Platform**: .NET 8.0 with .NET Standard 2.0 compatibility  
**License**: MIT License  
**Status**: Production-ready and actively maintained ✅ 