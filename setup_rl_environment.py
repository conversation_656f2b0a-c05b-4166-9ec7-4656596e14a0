#!/usr/bin/env python3
"""
Setup script for RL environment - WSC 2024 Port Optimization
"""
import subprocess
import sys
import os

def run_command(command):
    """Execute shell command and handle errors"""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {command}")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Error executing: {command}")
        print(f"Error: {e.stderr}")
        return None

def setup_python_environment():
    """Setup Python virtual environment and install dependencies"""
    print("🚀 Setting up RL environment for WSC 2024 Port Optimization...")
    
    # Create virtual environment
    run_command("python -m venv rl_port_env")
    
    # Activate and install dependencies
    if os.name == 'nt':  # Windows
        activate_cmd = "rl_port_env\\Scripts\\activate"
    else:  # Linux/Mac
        activate_cmd = "source rl_port_env/bin/activate"
    
    dependencies = [
        "stable-baselines3[extra]==2.0.0",
        "gymnasium==0.29.0",
        "numpy>=1.21.0",
        "pandas>=1.3.0",
        "matplotlib>=3.5.0",
        "requests>=2.28.0",
        "flask>=2.2.0",
        "tensorboard>=2.10.0",
        "optuna>=3.4.0",
        "pytest>=7.4.0"
    ]
    
    for dep in dependencies:
        run_command(f"{activate_cmd} && pip install {dep}")
    
    print("✅ Python environment setup complete!")

if __name__ == "__main__":
    setup_python_environment()