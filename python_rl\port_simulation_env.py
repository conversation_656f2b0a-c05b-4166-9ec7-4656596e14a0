import gymnasium as gym
import numpy as np
import requests
import json
from typing import Dict, Any, <PERSON><PERSON>, Optional
from gymnasium import spaces

class PortSimulationEnv(gym.Env):
    """
    Custom Gym environment for WSC 2024 Port Container Terminal Simulation
    """
    
    def __init__(self, simulation_endpoint: str = "http://localhost:5000", max_steps: int = 1000):
        super().__init__()
        
        self.simulation_endpoint = simulation_endpoint
        self.max_steps = max_steps
        self.current_step = 0
        
        # Define action space (as per plan)
        # MultiDiscrete([4, 24, 16]) + Box(shape=(4,), low=0, high=1)
        self.action_space = spaces.Dict({
            'discrete': spaces.MultiDiscrete([4, 24, 16]),  # berth, agv, yard_block
            'continuous': spaces.Box(low=0.0, high=1.0, shape=(4,), dtype=np.float32)
        })
        
        # Define observation space (~200 dimensions as planned)
        self.observation_space = spaces.Box(
            low=-np.inf, 
            high=np.inf, 
            shape=(200,), 
            dtype=np.float32
        )
        
        self.last_state = None
        self.session = requests.Session()
        
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """Reset the simulation environment"""
        super().reset(seed=seed)
        
        try:
            response = self.session.post(f"{self.simulation_endpoint}/api/simulation/reset")
            response.raise_for_status()
            
            state_data = response.json()
            self.last_state = state_data
            self.current_step = 0
            
            observation = self._encode_state(state_data)
            info = {"step": self.current_step, "simulation_time": state_data.get("currentTime", 0)}
            
            return observation, info
            
        except Exception as e:
            raise RuntimeError(f"Failed to reset simulation: {e}")
    
    def step(self, action: Dict) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """Execute one step in the simulation"""
        self.current_step += 1
        
        try:
            # Convert action to API format
            port_action = self._decode_action(action)
            
            # Send action to simulation
            response = self.session.post(
                f"{self.simulation_endpoint}/api/simulation/step",
                json=port_action
            )
            response.raise_for_status()
            
            step_result = response.json()
            
            # Extract components
            new_state = step_result["newState"]
            reward = step_result["reward"]
            done = step_result["done"] or self.current_step >= self.max_steps
            
            observation = self._encode_state(new_state)
            info = {
                "step": self.current_step,
                "simulation_time": new_state.get("currentTime", 0),
                "delay_rate": new_state.get("delayRate", 0),
                "total_delayed": new_state.get("totalDelayedVessels", 0)
            }
            
            self.last_state = new_state
            
            return observation, reward, done, False, info
            
        except Exception as e:
            # Return safe fallback
            observation = np.zeros(self.observation_space.shape, dtype=np.float32)
            return observation, -1000.0, True, False, {"error": str(e)}
    
    def _encode_state(self, state_data: Dict) -> np.ndarray:
        """Encode simulation state to observation vector (~200 dimensions)"""
        features = []
        
        # Vessel features (50 dimensions)
        vessels = state_data.get("vessels", [])
        for i in range(25):  # Max 25 vessels
            if i < len(vessels):
                vessel = vessels[i]
                features.extend([
                    vessel.get("arrivalTime", 0),
                    vessel.get("containerCount", 0)
                ])
            else:
                features.extend([0, 0])
        
        # Berth features (20 dimensions)
        berths = state_data.get("berths", [])
        for i in range(4):  # 4 berths
            if i < len(berths):
                berth = berths[i]
                features.extend([
                    1.0 if berth.get("isOccupied", False) else 0.0,
                    berth.get("utilizationRate", 0),
                    berth.get("availableQCs", 0),
                    0,  # Reserved for future use
                    0   # Reserved for future use
                ])
            else:
                features.extend([0, 0, 0, 0, 0])
        
        # AGV features (48 dimensions)
        agvs = state_data.get("agvs", [])
        for i in range(24):  # 24 AGVs
            if i < len(agvs):
                agv = agvs[i]
                features.extend([
                    1.0 if agv.get("isIdle", True) else 0.0,
                    1.0 if agv.get("hasContainer", False) else 0.0
                ])
            else:
                features.extend([0, 0])
        
        # QC features (48 dimensions)
        qcs = state_data.get("qcs", [])
        for i in range(12):  # 12 QCs
            if i < len(qcs):
                qc = qcs[i]
                features.extend([
                    1.0 if qc.get("isWorking", False) else 0.0,
                    qc.get("productivityRate", 0),
                    0,  # Reserved
                    0   # Reserved
                ])
            else:
                features.extend([0, 0, 0, 0])
        
        # Yard block features (32 dimensions)
        yard_blocks = state_data.get("yardBlocks", [])
        for i in range(16):  # 16 yard blocks
            if i < len(yard_blocks):
                block = yard_blocks[i]
                features.extend([
                    block.get("utilizationRate", 0),
                    block.get("currentLoad", 0) / max(block.get("capacity", 1), 1)
                ])
            else:
                features.extend([0, 0])
        
        # Global features (2 dimensions)
        features.extend([
            state_data.get("currentTime", 0) / 10080,  # Normalized by week
            state_data.get("delayRate", 0) / 100       # Normalized delay rate
        ])
        
        # Pad or truncate to exactly 200 dimensions
        while len(features) < 200:
            features.append(0.0)
        features = features[:200]
        
        return np.array(features, dtype=np.float32)
    
    def _decode_action(self, action: Dict) -> Dict:
        """Convert gym action to simulation API format"""
        discrete_actions = action['discrete']
        continuous_actions = action['continuous']
        
        return {
            "berthAllocation": int(discrete_actions[0]),
            "agvAssignment": int(discrete_actions[1]),
            "yardBlockSelection": int(discrete_actions[2]),
            "priorityWeights": continuous_actions[:3].tolist(),
            "resourceAllocationBias": float(continuous_actions[3]),
            "congestionAvoidance": 0.5  # Default value
        }
    
    def close(self):
        """Clean up resources"""
        if hasattr(self, 'session'):
            self.session.close()