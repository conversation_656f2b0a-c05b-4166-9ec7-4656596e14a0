﻿//
// This is the winner team's strategy. 
//
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WSC_SimChallenge_2024_Net.PortSimulation.Entity;

namespace WSC_SimChallenge_2024_Net.PortSimulation
{
    public class DecisionMaker
    {
        public static PortSimModel WSCPort { get; set; }
        private static Dictionary<AGV, Container> nextAcPair = new Dictionary<AGV, Container>();

        private static int countSingleIdleBerth = 0;  // Counter for when only one berth is idle
        private static int functionCallCount = 0;  // Counter to track the number of function calls

        public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
        {
            Berth allocatedBerth = null;

            functionCallCount++;
            double minTotalDistance = double.MaxValue; // Initialize with a large value
            List<Berth> currentIdleBerths = WSCPort.berthBeingIdle.CompletedList;

            // Debug: Log the number of idle berths and the current vessel's ID
            //Console.WriteLine($"Vessel ID: {arrivalVessel.Id}, Number of Idle Berths: {currentIdleBerths.Count}");

            // If only one berth is idle, return it directly
            if (currentIdleBerths.Count == 1)
            {
                countSingleIdleBerth++;
                //Console.WriteLine($"Only one idle berth available, assigning it directly: {currentIdleBerths[0].Id}");
                return currentIdleBerths[0];
            }

            // Loop through each idle berth
            foreach (var berth in currentIdleBerths)
            {
                double totalLoadingDistance = 0;
                ControlPoint berthCP = berth.EquippedQCs[1].CP; // Assume the berth has 3 QCs, pick the middle one

                // Debug: Log the start of calculation for the current berth
                //Console.WriteLine($"Calculating total loading distance for Berth: {berth.Id}");

                // Iterate over all yard blocks
                foreach (var yardBlock in WSCPort.YardBlocks)
                {
                    foreach (var container in yardBlock.StackedContainers)
                    {
                        // Check loading
                        if (container.LoadingVesselID == arrivalVessel.Id)
                        {
                            double distance = AGV.CalculateDistance(yardBlock.CP, berthCP);
                            totalLoadingDistance += distance;

                            // Debug: Log the distance for each container
                            //Console.WriteLine($"Container from YardBlock: {yardBlock.Id}, Distance to Berth: {distance}");
                        }
                    }
                }


                //Console.WriteLine($"Total Loading Distance for Berth {berth.Id}: {totalLoadingDistance}");

                if (totalLoadingDistance < minTotalDistance)
                {
                    minTotalDistance = totalLoadingDistance;
                    allocatedBerth = berth;


                    //Console.WriteLine($"Berth {berth.Id} is currently the best option with Total Distance: {totalLoadingDistance}");
                }
            }


            if (allocatedBerth != null)
            {
                //Console.WriteLine($"Final Selected Berth: {allocatedBerth.Id} with Total Distance: {minTotalDistance}");
            }
            else
            {
                //Console.WriteLine($"No suitable berth found, defaulting to first idle berth: {currentIdleBerths[0].Id}");
                allocatedBerth = currentIdleBerths.FirstOrDefault();
            }


            if (functionCallCount % 30 == 0)
            {
                //Console.WriteLine($"In the last 30 calls, there were {countSingleIdleBerth} instances where only one idle berth was available.");
                countSingleIdleBerth = 0;  // Reset the counter
            }

            return allocatedBerth;
        }

        public static AGV CustomeizedAllocatedAGVs(Container container)
        {
            AGV allocatedAGV = null;

            List<AGV> currentIdleAGVs = WSCPort.agvBeingIdle.CompletedList;
            // Return null if no idle AGV
            if (currentIdleAGVs.Count == 0) return null;
            // Define State Space for DQL
            double[] state = new double[3];
            state[0] = container.CurrentLocation.Xcoordinate;  // Container X-coordinate
            state[1] = container.CurrentLocation.Ycoordinate;  // Container Y-coordinate
            state[2] = currentIdleAGVs.Count;  // Number of available AGVs

            // Placeholder for action-value storage
            double minScore = double.MaxValue;
            Dictionary<AGV, double> agvScores = new Dictionary<AGV, double>();

            double distanceMin = 0; // 最短距离可能为0
            double distanceMax = 2033; 

            // Iterate through idle AGVs to compute action values (Q-values)
            foreach (var agv in currentIdleAGVs)
            {
                // Calculate distance from AGV to container
                double distanceScore = AGV.CalculateDistance(agv.CurrentLocation, container.CurrentLocation);
                double distanceNormalized = (distanceScore - distanceMin) / (distanceMax - distanceMin);

                // Compute resource efficiency around AGV (nearby QCs, YardBlocks)
                double nearbyResourcesScore = CalculateNearbyResourcesScore(agv);

                // Combine distance and resource scores for total action score
                double totalScore = distanceNormalized * 0.9 + nearbyResourcesScore * 0.1;

                // Store scores for Q-value update
                agvScores[agv] = totalScore;

                // Select AGV with the lowest score (best Q-value)
                if (totalScore < minScore)
                {
                    minScore = totalScore;
                    allocatedAGV = agv;
                }
            }

            // Output debug information for visualization and learning feedback
            //Console.WriteLine($"Selected AGV: {allocatedAGV?.Id} for Container: {container.Id}");
            //Console.WriteLine($"State: Container Location: ({state[0]}, {state[1]}), Idle AGVs: {state[2]}");

            // Return the best AGV based on the computed Q-values

            return allocatedAGV;
            //return null;
        }

        private static double CalculateNearbyResourcesScore(AGV agv)
        {
            // Parameters for detection radius and max resources
            double detectionRadius = 350.0;
            double minResources = 0; // 最少资源得分为0
            double maxResources = 20.0;

            // Calculate nearby QCs, YardBlocks, and AGVs
            int nearbyQCs = WSCPort.QCs
                .Count(QC => AGV.CalculateDistance(QC.CP, agv.CurrentLocation) <= detectionRadius && !WSCPort.qcBeingIdle.CompletedList.Contains(QC));

            int nearbyYardBlocks = WSCPort.YardBlocks
                .Count(block => AGV.CalculateDistance(block.CP, agv.CurrentLocation) <= detectionRadius && HasActiveTasks(block));

            int nearbyAGVs = WSCPort.agvBeingIdle.CompletedList
                .Count(otherAGV => AGV.CalculateDistance(otherAGV.CurrentLocation, agv.CurrentLocation) <= detectionRadius);

            // Compute total resources score with weighting factors
            double totalResources = 5 * nearbyQCs + 5 * nearbyYardBlocks - 10 * nearbyAGVs;

            // Normalize resource score
            // double resourcesScore = totalResources / maxResources;
            double resourcesNormalized = (totalResources - minResources) / (maxResources - minResources);
            return resourcesNormalized;
        }

        // Helper function to check active tasks for YardBlock
        private static bool HasActiveTasks(YardBlock block)
        {
            return block.ReservedSlots > 0;
        }

        public static YardBlock CustomeizedDetermineYardBlock(AGV agv)
        {
            YardBlock yardBlock = null;

            yardBlock = DetermineYB(agv);

            return yardBlock;
            //return null;
        }

        private static YardBlock DetermineYB(AGV agv)
        {
            YardBlock yardBlock = null;
            List<YardBlock> yardBlocks = WSCPort.YardBlocks;

            var availableBlocks = yardBlocks.FindAll(block => block.Capacity > block.ReservedSlots + block.StackedContainers.Count);

            if (availableBlocks.Count == 0)
            {
                Console.WriteLine("没有可用的堆场区块");
                return null;
            }

            var sortedBlocks = availableBlocks.OrderBy(block => AGV.CalculateDistance(block.CP, agv.CurrentLocation)).ToList();

            List<YardBlock> candidateBlocks = sortedBlocks.Take(5).ToList();

            double minTimeCost = double.MaxValue;
            YardBlock bestBlock = null;

            foreach (var block in candidateBlocks)
            {
                double waitTimeForYC = CalculateWaitTimeForYC(block);
                double transportTime = AGV.CalculateDistance(block.CP, agv.CurrentLocation) / agv.Speed;
                double totalTimeCost = waitTimeForYC + transportTime;
                if (totalTimeCost < minTimeCost)
                {
                    minTimeCost = totalTimeCost;
                    bestBlock = block;
                }
            }
            return bestBlock;
        }


        private static double CalculateWaitTimeForYC(YardBlock block)
        {
            int reservedSlots = block.ReservedSlots;
            double processingTimePerContainer = 90;
            int totalWorkload = reservedSlots;
            double estimatedWaitTime = totalWorkload * processingTimePerContainer;

            return estimatedWaitTime;
        }

        // Constructors are no longer needed since all methods are static
        // public DecisionMaker(PortSimModel wSCPort)
        // {
        //     WSCPort = wSCPort;
        // }

        // public DecisionMaker() { }
    }
}
