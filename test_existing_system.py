#!/usr/bin/env python3
"""
Test existing WSC simulation system before RL integration
"""
import subprocess
import os
import sys
import re

def test_current_simulation():
    """Test the current simulation runs correctly"""
    print("🔍 Testing Current Simulation System...")
    
    try:
        # Run the existing simulation
        result = subprocess.run(
            ["dotnet", "run"],
            capture_output=True,
            text=True,
            timeout=60,  # 60 second timeout
            cwd="."
        )
        
        if result.returncode == 0:
            output = result.stdout
            print("✅ Simulation runs successfully")
            
            # Extract key metrics from output
            delay_match = re.search(r"Rate of delayed vessels: ([\d.]+) %", output)
            vessel_match = re.search(r"Number of delayed vessels: (\d+); Number of arrival vessels: (\d+)", output)
            
            if delay_match and vessel_match:
                delay_rate = float(delay_match.group(1))
                delayed_vessels = int(vessel_match.group(1))
                total_vessels = int(vessel_match.group(2))
                
                print(f"📊 Current Performance:")
                print(f"   Delay Rate: {delay_rate}%")
                print(f"   Delayed Vessels: {delayed_vessels}/{total_vessels}")
                
                # Verify this matches expected baseline
                if abs(delay_rate - 11.67) < 0.1:
                    print("✅ Performance matches expected baseline (11.67%)")
                else:
                    print(f"⚠️  Performance differs from expected baseline: {delay_rate}% vs 11.67%")
                
                return True, delay_rate, delayed_vessels, total_vessels
            else:
                print("⚠️  Could not extract performance metrics from output")
                print("Output:", output[-500:])  # Last 500 chars
                return True, None, None, None
        else:
            print(f"❌ Simulation failed: {result.stderr}")
            return False, None, None, None
            
    except subprocess.TimeoutExpired:
        print("❌ Simulation timed out (>60 seconds)")
        return False, None, None, None
    except Exception as e:
        print(f"❌ Simulation error: {e}")
        return False, None, None, None

def test_decision_maker_structure():
    """Test DecisionMaker.cs structure for RL integration points"""
    print("🔍 Testing DecisionMaker Structure...")
    
    try:
        decision_maker_path = "StrategyMaking/DecisionMaker.cs"
        
        if not os.path.exists(decision_maker_path):
            print(f"❌ DecisionMaker.cs not found at {decision_maker_path}")
            return False
        
        with open(decision_maker_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key methods that we'll need to integrate with
        required_methods = [
            "CustomeizedAllocatedBerth",
            "CustomeizedAllocatedAGVs", 
            "CustomeizedDetermineYardBlock"
        ]
        
        found_methods = []
        for method in required_methods:
            if method in content:
                found_methods.append(method)
                print(f"✅ Found method: {method}")
            else:
                print(f"❌ Missing method: {method}")
        
        if len(found_methods) == len(required_methods):
            print("✅ All required methods found for RL integration")
            return True
        else:
            print(f"⚠️  Only {len(found_methods)}/{len(required_methods)} methods found")
            return False
            
    except Exception as e:
        print(f"❌ Error reading DecisionMaker.cs: {e}")
        return False

def check_project_dependencies():
    """Check if project has necessary dependencies for API integration"""
    print("🔍 Checking Project Dependencies...")
    
    try:
        # Check .csproj file for web dependencies
        csproj_files = [f for f in os.listdir('.') if f.endswith('.csproj')]
        
        if not csproj_files:
            print("❌ No .csproj file found")
            return False
        
        csproj_path = csproj_files[0]
        with open(csproj_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for web-related packages
        web_packages = [
            "Microsoft.AspNetCore",
            "Microsoft.Extensions.Hosting"
        ]
        
        has_web_support = any(pkg in content for pkg in web_packages)
        
        if has_web_support:
            print("✅ Project has web framework support")
        else:
            print("⚠️  Project may need web framework packages for API")
        
        print(f"✅ Project file: {csproj_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error checking dependencies: {e}")
        return False

def main():
    """Run all pre-integration tests"""
    print("🚀 Pre-Integration Testing for RL Implementation")
    print("=" * 60)
    
    # Test 1: Current simulation
    print("\n📋 Test 1: Current Simulation")
    print("-" * 40)
    sim_success, delay_rate, delayed, total = test_current_simulation()
    
    # Test 2: DecisionMaker structure
    print("\n📋 Test 2: DecisionMaker Structure")
    print("-" * 40)
    structure_success = test_decision_maker_structure()
    
    # Test 3: Project dependencies
    print("\n📋 Test 3: Project Dependencies")
    print("-" * 40)
    deps_success = check_project_dependencies()
    
    # Summary
    print("\n📊 Pre-Integration Test Summary")
    print("=" * 60)
    
    tests = [
        ("Current Simulation", sim_success),
        ("DecisionMaker Structure", structure_success),
        ("Project Dependencies", deps_success)
    ]
    
    passed = 0
    for test_name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 System ready for RL integration!")
        if delay_rate is not None:
            print(f"📊 Baseline to improve: {delay_rate}% delay rate")
        print("\n🔧 Next steps:")
        print("   1. Add web API support to .NET project")
        print("   2. Implement simulation service interface")
        print("   3. Create RL integration points in DecisionMaker")
    else:
        print("\n⚠️  Please resolve issues before RL integration")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)