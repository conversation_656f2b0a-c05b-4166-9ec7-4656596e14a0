using Microsoft.AspNetCore.Mvc;
using WSC_SimChallenge_2024_Net.PortSimulation.Entity;
using WSC_SimChallenge_2024_Net.PortSimulation.RL;

namespace WSC_SimChallenge_2024_Net.PortSimulation.API
{
    [ApiController]
    [Route("api/simulation")]
    public class SimulationController : ControllerBase
    {
        private readonly ISimulationService _simulationService;
        
        public SimulationController(ISimulationService simulationService)
        {
            _simulationService = simulationService;
        }

        [HttpPost("reset")]
        public ActionResult<SimulationState> Reset()
        {
            try
            {
                var state = _simulationService.Reset();
                return Ok(state);
            }
            catch (Exception ex)
            {
                return BadRequest($"Reset failed: {ex.Message}");
            }
        }

        [HttpPost("step")]
        public ActionResult<StepResult> Step([FromBody] PortAction action)
        {
            try
            {
                var result = _simulationService.Step(action);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"Step failed: {ex.Message}");
            }
        }

        [HttpGet("state")]
        public ActionResult<SimulationState> GetCurrentState()
        {
            try
            {
                var state = _simulationService.GetCurrentState();
                return Ok(state);
            }
            catch (Exception ex)
            {
                return BadRequest($"Get state failed: {ex.Message}");
            }
        }

        [HttpGet("metrics")]
        public ActionResult<PerformanceMetrics> GetMetrics()
        {
            try
            {
                var metrics = _simulationService.GetPerformanceMetrics();
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                return BadRequest($"Get metrics failed: {ex.Message}");
            }
        }
    }
}