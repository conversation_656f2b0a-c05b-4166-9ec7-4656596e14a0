//
// RL-Enhanced Decision Maker that integrates with Python RL service
// Falls back to original heuristic strategies when RL service is unavailable
//
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading.Tasks;
using WSC_SimChallenge_2024_Net.PortSimulation.Entity;

namespace WSC_SimChallenge_2024_Net.PortSimulation
{
    class DecisionMaker
    {
        public static PortSimModel WSCPort { get; set; }
        private static readonly HttpClient httpClient = new HttpClient();
        private static readonly string RL_SERVICE_URL = "http://localhost:5001";
        private static bool rlServiceAvailable = false;

        static DecisionMaker()
        {
            httpClient.Timeout = TimeSpan.FromSeconds(2); // Quick timeout for RL service
            // Initialize RL service availability check in background
            Task.Run(CheckRLServiceAvailability);
        }

        private static async Task CheckRLServiceAvailability()
        {
            try
            {
                var response = await httpClient.GetAsync($"{RL_SERVICE_URL}/health");
                rlServiceAvailable = response.IsSuccessStatusCode;
                Console.WriteLine($"🤖 RL Service: {(rlServiceAvailable ? "Available" : "Unavailable")}");
            }
            catch
            {
                rlServiceAvailable = false;
                Console.WriteLine("🤖 RL Service: Unavailable (using heuristic fallback)");
            }
        }
        
        public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
        {
            List<Berth> currentIdleBerths = WSCPort.berthBeingIdle.CompletedList;
            
            if (currentIdleBerths == null || currentIdleBerths.Count == 0)
                return null;

            // Try RL-based decision first
            if (rlServiceAvailable)
            {
                var rlBerth = GetRLBerthDecision(arrivalVessel, currentIdleBerths);
                if (rlBerth != null)
                    return rlBerth;
            }
            
            // Fallback to original heuristic strategy
            return GetHeuristicBerthDecision(arrivalVessel, currentIdleBerths);
        }
        
        private static Berth GetRLBerthDecision(Vessel vessel, List<Berth> availableBerths)
        {
            try
            {
                // Update RL service with current simulation state
                UpdateRLServiceState();
                
                // Prepare request data
                var requestData = new
                {
                    vessel = new
                    {
                        id = vessel.Id,
                        containerCount = vessel.DischargingContainersInformation.Values.Sum() +
                                       (vessel.LoadingContainersInformation?.Values.Sum() ?? 0),
                        arrivalTime = vessel.ArrivalTime
                    },
                    available_berths = availableBerths.Select(b => b.Id).ToArray()
                };
                
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = httpClient.PostAsync($"{RL_SERVICE_URL}/berth_decision", content).Result;
                
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = response.Content.ReadAsStringAsync().Result;
                    var decision = JsonSerializer.Deserialize<RLDecision>(responseJson);
                    
                    if (decision?.berth_id != null)
                    {
                        var selectedBerth = availableBerths.FirstOrDefault(b => b.Id == decision.berth_id);
                        if (selectedBerth != null)
                        {
                            Console.WriteLine($"🤖 RL Berth Decision: {decision.berth_id} (confidence: {decision.confidence:F2})");
                            return selectedBerth;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ RL Berth Decision failed: {ex.Message}");
                rlServiceAvailable = false; // Disable RL service temporarily
            }
            
            return null;
        }
        
        public static AGV CustomeizedAllocatedAGVs(Container container)
        {
            List<AGV> currentIdleAGVs = WSCPort.agvBeingIdle.CompletedList;
            
            if (currentIdleAGVs == null || currentIdleAGVs.Count == 0)
                return null;

            // Try RL-based decision first
            if (rlServiceAvailable)
            {
                var rlAGV = GetRLAGVDecision(container, currentIdleAGVs);
                if (rlAGV != null)
                    return rlAGV;
            }
            
            // Fallback to original heuristic strategy
            return GetHeuristicAGVDecision(container, currentIdleAGVs);
        }
        
        private static AGV GetRLAGVDecision(Container container, List<AGV> availableAGVs)
        {
            try
            {
                var requestData = new
                {
                    container = new
                    {
                        id = container.Id,
                        currentLocation = new { x = container.CurrentLocation.Xcoordinate, y = container.CurrentLocation.Ycoordinate },
                        priority = GetContainerPriority(container)
                    },
                    available_agvs = availableAGVs.Select(a => a.Id).ToArray()
                };
                
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = httpClient.PostAsync($"{RL_SERVICE_URL}/agv_decision", content).Result;
                
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = response.Content.ReadAsStringAsync().Result;
                    var decision = JsonSerializer.Deserialize<RLDecision>(responseJson);
                    
                    if (decision?.agv_id != null)
                    {
                        var selectedAGV = availableAGVs.FirstOrDefault(a => a.Id == decision.agv_id);
                        if (selectedAGV != null)
                        {
                            Console.WriteLine($"🤖 RL AGV Decision: {decision.agv_id} (confidence: {decision.confidence:F2})");
                            return selectedAGV;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ RL AGV Decision failed: {ex.Message}");
                rlServiceAvailable = false;
            }
            
            return null;
        }
        

        

        
        private static void UpdateRLServiceState()
        {
            try
            {
                var stateData = new
                {
                    currentTime = WSCPort.ClockTime,
                    vessels = WSCPort.Vessels.Select(v => new
                    {
                        id = v.Id,
                        containerCount = v.DischargingContainersInformation.Values.Sum(),
                        arrivalTime = v.ArrivalTime
                    }).ToArray(),
                    berths = WSCPort.Berths.Select(b => new
                    {
                        id = b.Id,
                        isOccupied = b.BerthedVessel != null,
                        availableQCs = b.EquippedQCs.Count(qc => qc.ServedVessel == null)
                    }).ToArray(),
                    delayRate = CalculateCurrentDelayRate()
                };

                var json = JsonSerializer.Serialize(stateData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                httpClient.PostAsync($"{RL_SERVICE_URL}/update_state", content);
            }
            catch
            {
                // Silently fail state updates
            }
        }
        
        private static double CalculateCurrentDelayRate()
        {
            var totalVessels = WSCPort.Vessels.Count;
            if (totalVessels == 0) return 0.0;

            // Simple delay calculation based on arrival vs start berthing time
            var delayedVessels = WSCPort.Vessels.Count(v =>
                v.StartBerthingTime != default(DateTime) &&
                v.StartBerthingTime > v.ArrivalTime.AddHours(1)); // 1 hour tolerance
            return (double)delayedVessels / totalVessels * 100.0;
        }
        
        // Fallback heuristic methods (original strategies)
        private static Berth GetHeuristicBerthDecision(Vessel vessel, List<Berth> availableBerths)
        {
            // Original berth selection logic from DecisionMaker.cs
            int totalContainerWork = vessel.DischargingContainersInformation.Values.Sum() + 
                                   (vessel.LoadingContainersInformation?.Values.Sum() ?? 0);
            
            Berth bestBerth = null;
            double bestScore = double.MinValue;
            
            foreach (var berth in availableBerths)
            {
                double score = CalculateBerthScore(berth, vessel, totalContainerWork);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestBerth = berth;
                }
            }
            
            return bestBerth;
        }
        
        private static double CalculateBerthScore(Berth berth, Vessel vessel, int workload)
        {
            double score = 0;
            
            int availableQCs = berth.EquippedQCs.Count(qc => qc.ServedVessel == null);
            score += availableQCs * 100;
            
            int berthId = int.Parse(berth.Id.Replace("berth", ""));
            if (berthId == 1 || berthId == 2)
                score += 30;
            else if (berthId == 0 || berthId == 3)
                score += 20;
            
            if (workload > 100)
                score += availableQCs * 50;
            
            int currentQCRotation = berth.CurrentWorkQC;
            score += (3 - currentQCRotation) * 10;
            
            return score;
        }
        
        private static AGV GetHeuristicAGVDecision(Container container, List<AGV> availableAGVs)
        {
            // Original AGV selection logic
            AGV bestAGV = null;
            double bestScore = double.MinValue;
            
            foreach (var agv in availableAGVs)
            {
                double score = CalculateAGVScore(agv, container);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestAGV = agv;
                }
            }
            
            return bestAGV;
        }
        
        private static double CalculateAGVScore(AGV agv, Container container)
        {
            double score = 0;
            
            double distance = AGV.CalculateDistance(agv.CurrentLocation, container.CurrentLocation);
            score += (2000 - distance) * 0.1;
            
            int agvWorkload = agv.InDischarging ? 2 : 0;
            if (agv.CurrentLocation.Ycoordinate <= 50) agvWorkload += 1;
            score += (10 - agvWorkload) * 10;
            
            int containerPriority = GetContainerPriority(container);
            score += containerPriority * 5;
            
            return score;
        }
        

        private static int GetContainerPriority(Container container)
        {
            // Simple priority calculation
            return 1;
        }

        public static YardBlock CustomeizedDetermineYardBlock(AGV agv)
        {
            // Simple heuristic: find first available yard block with capacity
            List<YardBlock> yardBlocks = WSCPort.YardBlocks;

            var availableBlock = yardBlocks.FirstOrDefault(block =>
                block.Capacity > block.ReservedSlots + block.StackedContainers.Count);

            return availableBlock;
        }
    }
    
    // Data transfer objects for RL service communication
    public class RLDecision
    {
        public string berth_id { get; set; }
        public string agv_id { get; set; }
        public string yard_block_id { get; set; }
        public double confidence { get; set; }
        public string method { get; set; }
    }
}
