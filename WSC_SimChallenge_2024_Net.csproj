﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="conf\transhipment_round1.csv" />
    <None Remove="conf\vessel_arrival_time_round1.csv" />
    <None Remove="conf\vessel_weekly_arrival.csv" />
    <None Remove="vessel_arrival_time.csv" />
    <None Remove="vessel_weekly_arrival.csv" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="conf\transhipment_round1.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="conf\vessel_arrival_time_round1.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="O2DESNet" Version="3.7.1" />
  </ItemGroup>

  <ItemGroup>
    <None Update="conf\QC_controlpoint.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="conf\YC_controlpoint.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="PortSimulation\vessel_weekly_arrival.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
