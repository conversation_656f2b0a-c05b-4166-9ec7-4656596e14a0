namespace WSC_SimChallenge_2024_Net.PortSimulation.RL
{
    public class RewardCalculator
    {
        public static double CalculateReward(SimulationState prevState, PortAction action, SimulationState newState)
        {
            // Primary objective: Minimize vessel delays
            double delayPenalty = -100.0 * (newState.TotalDelayedVessels - (prevState?.TotalDelayedVessels ?? 0));
            double delayImprovement = 50.0 * Math.Max(0, (prevState?.DelayRate ?? 0) - newState.DelayRate);
            
            // Secondary objectives
            double throughputReward = CalculateThroughputReward(newState);
            double efficiencyReward = CalculateEfficiencyReward(newState);
            
            // Operational penalties
            double congestionPenalty = CalculateCongestionPenalty(newState);
            double resourceConflictPenalty = CalculateResourceConflictPenalty(newState);
            
            // Long-term incentives
            double learningBonus = newState.DelayRate < 11.67 ? 5.0 : 0.0; // Better than baseline
            
            // Shaped reward for better learning
            double totalReward = delayPenalty + delayImprovement + 
                               throughputReward + efficiencyReward +
                               congestionPenalty + resourceConflictPenalty +
                               learningBonus;
            
            // Prevent extreme values
            return Math.Max(-1000, Math.Min(1000, totalReward));
        }
        
        private static double CalculateThroughputReward(SimulationState state)
        {
            // Calculate containers processed this step
            int containersProcessed = 0;
            foreach (var qc in state.QCs)
            {
                if (qc.IsWorking)
                    containersProcessed++;
            }
            return 1.0 * containersProcessed;
        }
        
        private static double CalculateEfficiencyReward(SimulationState state)
        {
            // Calculate resource utilization improvement
            double totalUtilization = 0;
            int resourceCount = 0;
            
            foreach (var berth in state.Berths)
            {
                totalUtilization += berth.UtilizationRate;
                resourceCount++;
            }
            
            foreach (var block in state.YardBlocks)
            {
                totalUtilization += block.UtilizationRate;
                resourceCount++;
            }
            
            double avgUtilization = resourceCount > 0 ? totalUtilization / resourceCount : 0;
            return 10.0 * avgUtilization;
        }
        
        private static double CalculateCongestionPenalty(SimulationState state)
        {
            // Simple congestion score based on AGV idle rate
            int idleAGVs = state.AGVs.Count(agv => agv.IsIdle);
            double congestionScore = 1.0 - (double)idleAGVs / state.AGVs.Length;
            return -5.0 * congestionScore;
        }
        
        private static double CalculateResourceConflictPenalty(SimulationState state)
        {
            // Penalty for resource conflicts (simplified)
            int conflicts = 0;
            
            // Check for over-utilized yard blocks
            foreach (var block in state.YardBlocks)
            {
                if (block.UtilizationRate > 0.9)
                    conflicts++;
            }
            
            return -20.0 * conflicts;
        }
    }
}