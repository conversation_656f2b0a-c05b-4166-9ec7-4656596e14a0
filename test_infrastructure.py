#!/usr/bin/env python3
"""
Infrastructure Testing Script for RL Implementation
Tests basic components before moving to next phase
"""
import os
import sys
import subprocess
import requests
import json
import time

def test_python_environment():
    """Test Python environment setup"""
    print("🔍 Testing Python Environment...")
    
    try:
        import gymnasium
        import stable_baselines3
        import numpy as np
        import requests
        print("✅ All Python dependencies available")
        return True
    except ImportError as e:
        print(f"❌ Missing Python dependency: {e}")
        return False

def test_dotnet_project():
    """Test .NET project compilation"""
    print("🔍 Testing .NET Project...")
    
    try:
        # Test if project builds
        result = subprocess.run(
            ["dotnet", "build", "--configuration", "Debug"],
            capture_output=True,
            text=True,
            cwd="."
        )
        
        if result.returncode == 0:
            print("✅ .NET project builds successfully")
            return True
        else:
            print(f"❌ .NET build failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ .NET build error: {e}")
        return False

def test_gym_environment():
    """Test custom Gym environment"""
    print("🔍 Testing Gym Environment...")
    
    try:
        # Import our custom environment
        sys.path.append('./python_rl')
        from port_simulation_env import PortSimulationEnv
        
        # Create environment (without connecting to simulation yet)
        env = PortSimulationEnv()
        
        # Test action and observation spaces
        print(f"Action space: {env.action_space}")
        print(f"Observation space: {env.observation_space}")
        
        # Test action sampling
        sample_action = env.action_space.sample()
        print(f"Sample action: {sample_action}")
        
        print("✅ Gym environment structure is valid")
        return True
        
    except Exception as e:
        print(f"❌ Gym environment error: {e}")
        return False

def test_data_models():
    """Test RL data model serialization"""
    print("🔍 Testing Data Models...")
    
    # Create sample data structures
    sample_state = {
        "vessels": [
            {"id": "vessel1", "arrivalTime": 0, "containerCount": 100, "isDelayed": False, "waitingTime": 0, "assignedBerth": 0}
        ],
        "berths": [
            {"id": "berth0", "isOccupied": True, "occupiedByVessel": "vessel1", "utilizationRate": 0.8, "availableQCs": 2}
        ],
        "agvs": [
            {"id": "agv1", "isIdle": False, "hasContainer": True, "position": [100, 200], "distanceToTarget": 50}
        ],
        "qcs": [
            {"id": "qc1", "isWorking": True, "assignedBerth": "berth0", "productivityRate": 0.9}
        ],
        "yardBlocks": [
            {"id": "block1", "capacity": 1000, "currentLoad": 500, "utilizationRate": 0.5, "reservedSlots": 50}
        ],
        "currentTime": 3600,
        "totalDelayedVessels": 0,
        "delayRate": 0.0
    }
    
    sample_action = {
        "berthAllocation": 1,
        "agvAssignment": 5,
        "yardBlockSelection": 3,
        "priorityWeights": [0.5, 0.3, 0.2],
        "resourceAllocationBias": 0.7,
        "congestionAvoidance": 0.4
    }
    
    try:
        # Test JSON serialization
        state_json = json.dumps(sample_state)
        action_json = json.dumps(sample_action)
        
        # Test deserialization
        parsed_state = json.loads(state_json)
        parsed_action = json.loads(action_json)
        
        print("✅ Data model serialization works")
        return True
        
    except Exception as e:
        print(f"❌ Data model error: {e}")
        return False

def test_reward_calculation():
    """Test reward calculation logic"""
    print("🔍 Testing Reward Calculation...")
    
    try:
        # Create mock states for testing
        prev_state = {
            "totalDelayedVessels": 5,
            "delayRate": 15.0
        }
        
        new_state = {
            "totalDelayedVessels": 4,
            "delayRate": 12.0,
            "qcs": [{"isWorking": True}, {"isWorking": False}],
            "berths": [{"utilizationRate": 0.8}],
            "yardBlocks": [{"utilizationRate": 0.6}],
            "agvs": [{"isIdle": True}, {"isIdle": False}]
        }
        
        # Simple Python version of reward calculation for testing
        delay_penalty = -100.0 * (new_state["totalDelayedVessels"] - prev_state["totalDelayedVessels"])
        delay_improvement = 50.0 * max(0, prev_state["delayRate"] - new_state["delayRate"])
        learning_bonus = 5.0 if new_state["delayRate"] < 11.67 else 0.0
        
        total_reward = delay_penalty + delay_improvement + learning_bonus
        
        print(f"Sample reward calculation: {total_reward}")
        print("✅ Reward calculation logic works")
        return True
        
    except Exception as e:
        print(f"❌ Reward calculation error: {e}")
        return False

def run_all_tests():
    """Run all infrastructure tests"""
    print("🚀 Starting Infrastructure Tests for RL Implementation")
    print("=" * 60)
    
    tests = [
        ("Python Environment", test_python_environment),
        (".NET Project", test_dotnet_project),
        ("Gym Environment", test_gym_environment),
        ("Data Models", test_data_models),
        ("Reward Calculation", test_reward_calculation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        success = test_func()
        results.append((test_name, success))
        print()
    
    # Summary
    print("📊 Test Summary")
    print("=" * 60)
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All infrastructure tests passed! Ready for Phase 2.")
        return True
    else:
        print("⚠️  Some tests failed. Please fix issues before proceeding.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)