# WSC SimChallenge 2024 - Port Container Terminal Simulation

This project is part of the **World Simulation Conference (WSC) 2024 Simulation Challenge**, focusing on optimizing container terminal operations at a port facility. The simulation models the complex interactions between vessels, berths, quay cranes, automated guided vehicles (AGVs), and yard cranes in a discrete-event simulation environment.

## Project Overview

The simulation models a container port terminal with the following key components:
- **4 Berths** for vessel docking
- **12 Quay Cranes (QCs)** for container loading/unloading (3 per berth)
- **Configurable AGVs** for container transportation
- **Multiple Yard Cranes (YCs)** for container stacking operations
- **Dynamic vessel arrivals** with container discharge/loading requirements

### Objectives
- Minimize vessel waiting times and delays
- Optimize resource allocation (berths, cranes, AGVs)
- Maximize port throughput efficiency
- Implement intelligent decision-making strategies

## Technical Specifications

- **Framework**: .NET 8.0
- **Simulation Engine**: O2DESNet 3.7.1 (Discrete Event Simulation)
- **Language**: C#
- **Platform**: Cross-platform (Windows, Linux, macOS)

## Getting Started

### Prerequisites

**Important**: You need the .NET SDK (Software Development Kit), not just the runtime, to build and run this simulation.

- **[.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)** or later
  - Download the **SDK** version (not Runtime)
  - Verify installation by running: `dotnet --version`
  - Check available SDKs: `dotnet --list-sdks`
- Any IDE supporting C# (Visual Studio, VS Code, JetBrains Rider)

### Verification
After installing .NET SDK, verify your setup:
```bash
# Check .NET version
dotnet --version

# List installed SDKs
dotnet --list-sdks

# Expected output should show SDK version like: 8.0.xxx
```

### Installation

1. **Clone the repository:**
   ```bash
   git clone [repository-url]
   cd WSC_SimChallenge_2024_Net
   ```

2. **Restore dependencies:**
   ```bash
   dotnet restore
   ```

3. **Build the project:**
   ```bash
   dotnet build
   ```

4. **Run the simulation:**
   ```bash
   dotnet run
   ```

## Project Structure

```
WSC_SimChallenge_2024_Net/
├── Activity/                    # Base activity classes
├── conf/                       # Configuration files
│   ├── transhipment_round1.csv    # Container flow data
│   ├── vessel_arrival_time_round1.csv # Vessel schedules
│   ├── QC_controlpoint.csv        # Quay crane positions
│   └── YC_controlpoint.csv        # Yard crane positions
├── FileReader/                 # Data loading utilities
├── PortSimulation/
│   ├── Entity/                 # Simulation entities
│   │   ├── AGV.cs             # Automated Guided Vehicle
│   │   ├── Berth.cs           # Docking berth
│   │   ├── Container.cs       # Container cargo
│   │   ├── QC.cs              # Quay crane
│   │   ├── Vessel.cs          # Ships
│   │   └── YC.cs              # Yard crane
│   └── Model/
│       └── PortSimModel.cs    # Main simulation model
├── StrategyMaking/             # Decision algorithms
│   ├── DecisionMaker.cs       # Customizable strategies
│   └── Default.cs             # Default allocation logic
└── Program.cs                  # Application entry point
```

## Strategy Architecture & Fallback Mechanism

### 🏗️ **Dual-Layer Strategy System**

The simulation uses a robust dual-layer architecture that ensures reliability:

```
┌─────────────────────────────────────────────────────────────┐
│                    Simulation Engine                        │
│                                                             │
│  ┌───────────────────────┐    ┌─────────────────────────┐   │
│  │   DecisionMaker.cs    │    │      Default.cs         │   │
│  │  (Your Implementation) │───▶│   (Backup FIFO)        │   │
│  │                       │    │                         │   │
│  │ • CustomeizedAllocated │    │ • CustomeizedAllocated  │   │
│  │   Berth()             │    │   Berth()               │   │
│  │ • CustomeizedAllocated │    │ • CustomeizedAllocated  │   │
│  │   AGVs()              │    │   AGVs()                │   │
│  │ • CustomeizedDetermine │    │ • CustomeizedDetermine  │   │
│  │   YardBlock()         │    │   YardBlock()           │   │
│  └───────────────────────┘    └─────────────────────────┘   │
│             │                            │                  │
│             │ return null               │                  │
│             └──────────────────────────▶│                  │
│                                         │                  │
│                    Automatic Fallback   │                  │
└─────────────────────────────────────────────────────────────┘
```

### 🔧 **How Fallback Works**

1. **Primary Call**: Simulation calls your method in `DecisionMaker.cs`
2. **Success Case**: If you return a valid object, it's used immediately
3. **Fallback Case**: If you return `null`, the system automatically calls `Default.cs`
4. **Safety Net**: The simulation never fails due to your implementation

### 💻 **Implementation Examples**

#### **Basic Implementation (Safe)**
```csharp
public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
{
    // Return null to use default FIFO strategy
    return null;
}
```

#### **Partial Implementation (Recommended)**
```csharp
public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
{
    try
    {
        // Your optimization logic here
        Berth bestBerth = YourOptimizationAlgorithm(arrivalVessel);
        
        // Return your choice or fallback to default
        return bestBerth; // Could be null, which triggers fallback
    }
    catch
    {
        // Always safe to return null
        return null;
    }
}
```

#### **Advanced Implementation (Full Control)**
```csharp
public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
{
    // Your optimization logic
    Berth optimizedBerth = YourOptimizationAlgorithm(arrivalVessel);
    
    // Explicit fallback if your algorithm fails
    return optimizedBerth ?? Default.CustomeizedAllocatedBerth(arrivalVessel);
}
```

### 🎯 **Benefits of This Architecture**

- **Development Safety**: Never break the simulation during development
- **Incremental Development**: Implement one method at a time
- **Testing Flexibility**: Easy to compare your strategy vs. default
- **Robustness**: Production-ready system with automatic error handling
- **Performance**: Only fallback when necessary, minimal overhead

## Simulation Parameters

### Default Configuration
- **Simulation Duration**: 10 weeks
- **Warm-up Period**: 1 week
- **Number of Berths**: 4
- **QCs per Berth**: 3
- **AGVs**: 24 (configurable as 2x vessel groups)
- **Yard Block Capacity**: 1000 containers each
- **AGV Speed**: 4.5 m/s

### Key Performance Metrics
- **Vessel Delay Rate**: Percentage of vessels delayed >2 hours
- **Resource Utilization**: Berth, crane, and AGV efficiency
- **Container Throughput**: Total containers processed
- **Waiting Times**: Average vessel and container delays

## Challenge Expectations & Strategy Implementation

### 🎯 **What You Need to Do**

The WSC 2024 Challenge expects participants to **implement intelligent optimization strategies** to minimize vessel delays and maximize port efficiency. Your task is to replace the default FIFO (First-In-First-Out) algorithms with advanced decision-making logic.

### 📁 **Files to Modify**

#### **Primary Target: `StrategyMaking/DecisionMaker.cs`**
This is the **ONLY file** you should modify. It contains three key methods that you must implement:

1. **Berth Allocation Strategy** (`CustomeizedAllocatedBerth`)
2. **AGV Assignment Strategy** (`CustomeizedAllocatedAGVs`)  
3. **Yard Block Selection Strategy** (`CustomeizedDetermineYardBlock`)

#### **DO NOT Modify These Files:**
- ⚠️ **Core simulation logic** (PortSimulation/, Activity/, Program.cs)
- ⚠️ **Entity definitions** (Vessel.cs, Berth.cs, AGV.cs, etc.)
- ⚠️ **Configuration files** (conf/ directory)
- ⚠️ **Default strategies** (StrategyMaking/Default.cs)

### 🔧 **Fallback Strategy Architecture**

The system uses a **dual-layer strategy architecture** with automatic fallback:

```
DecisionMaker (Your Implementation) → Default (Backup FIFO)
```

**How it works:**
1. **Primary Layer**: Your optimization strategies in `DecisionMaker.cs`
2. **Fallback Layer**: If your method returns `null`, the system automatically uses `Default.cs`
3. **Safety Net**: Ensures the simulation never fails due to implementation issues

### 📋 **Required Method Implementations**

#### **1. Berth Allocation** (`CustomeizedAllocatedBerth`)
```csharp
public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
{
    // TODO: Implement your intelligent berth allocation strategy
    // Consider:
    // - Vessel workload (container count)
    // - Berth QC availability
    // - Expected service time
    // - Berth position optimization
    
    // Return null to use default FIFO strategy
    return null;
}
```

**Available Data:**
- `arrivalVessel.DischargingContainersInformation` - Containers to unload
- `arrivalVessel.LoadingContainersInformation` - Containers to load
- `WSCPort.berthBeingIdle.CompletedList` - Available berths
- `berth.EquippedQCs` - Berth's quay cranes
- `qc.ServedVessel` - QC availability status

#### **2. AGV Assignment** (`CustomeizedAllocatedAGVs`)
```csharp
public static AGV CustomeizedAllocatedAGVs(Container container)
{
    // TODO: Implement your intelligent AGV allocation strategy
    // Consider:
    // - Distance to container
    // - AGV workload and availability
    // - Traffic management
    // - Container priority (loading vs discharge)
    
    // Return null to use default distance-based strategy
    return null;
}
```

**Available Data:**
- `WSCPort.agvBeingIdle.CompletedList` - Available AGVs
- `agv.Position` - AGV current location
- `agv.InDischarging` - AGV operation mode
- `container.Week` - Container time priority
- `container.LoadingVesselID` - Container destination

#### **3. Yard Block Selection** (`CustomeizedDetermineYardBlock`)
```csharp
public static YardBlock CustomeizedDetermineYardBlock(AGV agv)
{
    // TODO: Implement your intelligent yard block allocation strategy
    // Consider:
    // - Block capacity and utilization
    // - Distance to block
    // - Container destination grouping
    // - Future retrieval efficiency
    
    // Return null to use default nearest-block strategy
    return null;
}
```

**Available Data:**
- `WSCPort.YardBlocks` - All yard blocks
- `block.Capacity` - Block maximum capacity
- `block.StackedContainers.Count` - Current utilization
- `block.ReservedSlots` - Reserved space
- `agv.LoadedContainer` - Container being transported

### 🚀 **Implementation Strategy**

#### **Step 1: Analyze Default Behavior**
Run the simulation to understand baseline performance:
```bash
dotnet run
```
Note the initial delay rate and identify bottlenecks.

#### **Step 2: Implement One Strategy at a Time**
Start with one method, test it, then move to the next:
1. Begin with berth allocation
2. Add AGV optimization
3. Implement yard block intelligence

#### **Step 3: Use Fallback During Development**
```csharp
public static Berth CustomeizedAllocatedBerth(Vessel arrivalVessel)
{
    // Your optimization logic here
    Berth optimizedBerth = YourOptimizationAlgorithm(arrivalVessel);
    
    // Safety fallback
    return optimizedBerth ?? Default.CustomeizedAllocatedBerth(arrivalVessel);
}
```

#### **Step 4: Validate and Iterate**
- Test each change immediately
- Compare delay rates before/after
- Revert changes that worsen performance

### 📊 **Success Metrics**

Your optimization will be evaluated based on:
- **Primary**: Vessel delay rate (% of vessels delayed >2 hours)
- **Secondary**: Resource utilization efficiency
- **Tertiary**: Total container throughput

### 🔍 **Available Resources**

**Simulation Data Access:**
- `WSCPort.berthBeingIdle.CompletedList` - Available berths
- `WSCPort.agvBeingIdle.CompletedList` - Available AGVs  
- `WSCPort.YardBlocks` - All yard blocks
- `WSCPort.Berths` - All berths
- `WSCPort.ContainersPending` - Containers waiting for service

**Utility Methods:**
- Distance calculations between positions
- Container workload assessment
- Resource availability checking
- Utilization rate calculations

### ⚠️ **Important Guidelines**

1. **Never break the simulation** - Always return valid objects or null
2. **Maintain deterministic behavior** - Avoid random decisions
3. **Consider performance** - Avoid computationally expensive operations
4. **Test incrementally** - Validate each change before proceeding
5. **Document your approach** - Comment your optimization strategies

### 🎯 **Challenge Goal**

**Minimize the vessel delay rate** while maintaining system stability and efficiency. A successful solution should demonstrate significant improvement over the default FIFO strategies while being robust and maintainable.

## Configuration Files

### Input Data Format

- **`transhipment_round1.csv`**: Container flow matrix between vessels
- **`vessel_arrival_time_round1.csv`**: Scheduled arrival times for each vessel
- **`QC_controlpoint.csv`**: X,Y coordinates for quay crane positions
- **`YC_controlpoint.csv`**: X,Y coordinates for yard crane positions

## Debugging and Analysis

Enable debug modes by uncommenting debug defines in `PortSimModel.cs`:
```csharp
#define DebugofBerth     // Berth utilization details
#define DebugofVessel    // Vessel movement tracking
#define DebugofQC        // Quay crane operations
#define DebugofAGV       // AGV movement and assignments
#define DebugofYC        // Yard crane activities
```

## Building and Testing

### Build the Project
```bash
dotnet build --configuration Release
```

### Run with Custom Parameters
Modify simulation parameters in `Program.cs`:
```csharp
PortSimModel WSCPort = new PortSimModel()
{
    NumberofAGVs = 24,  // Adjust AGV count
    StartTime = new DateTime(2024, 5, 4, 0, 0, 0)
};
```

### Performance Analysis
The simulation outputs key metrics including:
- Number of delayed vessels
- Vessel delay rate percentage
- Resource utilization statistics
- Flow balance validation

## Competition Guidelines

This simulation is designed for the WSC 2024 challenge. Participants must follow these guidelines:

### 🎯 **Core Requirements**
1. **Implement optimization strategies** in `StrategyMaking/DecisionMaker.cs` ONLY
2. **Maintain simulation integrity** - DO NOT modify core simulation logic
3. **Focus on efficiency** - minimize vessel delays and maximize throughput
4. **Document your approach** - explain your optimization strategies

### 📋 **Submission Requirements**
- **Modified Files**: Only `StrategyMaking/DecisionMaker.cs` should be changed
- **Strategy Documentation**: Include comments explaining your optimization logic
- **Performance Metrics**: Report baseline vs. optimized delay rates
- **Validation**: Ensure your solution works consistently across multiple runs

### 🔧 **Technical Constraints**
- **Deterministic Behavior**: Avoid random or time-based decisions
- **Null Safety**: Always handle null returns gracefully
- **Performance**: Optimize for execution speed as well as simulation results
- **Compatibility**: Ensure compatibility with existing fallback mechanisms

### 🏆 **Evaluation Criteria**
1. **Primary Metric**: Vessel delay rate reduction
2. **Code Quality**: Clean, maintainable, and well-documented code
3. **Innovation**: Creative and intelligent optimization approaches
4. **Robustness**: Stable performance across different scenarios

## Troubleshooting

### Common Issues

#### .NET SDK Issues
- **"No .NET SDKs were found"**: You have only the .NET Runtime installed
  - Solution: Download and install [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
  - Restart your command prompt after installation
- **"dotnet command not found"**: .NET is not in your system PATH
  - Windows: Add `C:\Program Files\dotnet` to your PATH environment variable
  - Restart command prompt and try again

#### Simulation Issues  
- **Configuration file not found**: Ensure CSV files are in the `conf/` directory
- **Simulation hangs**: Check for infinite loops in decision logic
- **Poor performance**: Review AGV allocation and yard block strategies

### Debug Output
Run with debug flags enabled to trace simulation events and identify bottlenecks.

## Contributing

This is a competition project. Focus on:
- Implementing efficient resource allocation algorithms
- Optimizing container flow strategies
- Analyzing performance bottlenecks
- Testing different parameter configurations

## License

This project is part of the WSC 2024 Simulation Challenge. Please refer to competition guidelines for usage restrictions.

## Contact

For questions about the simulation challenge, refer to the WSC 2024 competition organizers or relevant documentation provided with the challenge materials.