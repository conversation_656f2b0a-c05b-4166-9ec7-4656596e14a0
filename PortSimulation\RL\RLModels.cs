using System.Text.Json.Serialization;

namespace WSC_SimChallenge_2024_Net.PortSimulation.RL
{
    public class SimulationState
    {
        // Port state information (~200 dimensions as planned)
        public VesselState[] Vessels { get; set; } = Array.Empty<VesselState>();
        public BerthState[] Berths { get; set; } = Array.Empty<BerthState>();
        public AGVState[] AGVs { get; set; } = Array.Empty<AGVState>();
        public QCState[] QCs { get; set; } = Array.Empty<QCState>();
        public YardBlockState[] YardBlocks { get; set; } = Array.Empty<YardBlockState>();
        public double CurrentTime { get; set; }
        public int TotalDelayedVessels { get; set; }
        public double DelayRate { get; set; }
    }

    public class PortAction
    {
        // Primary decisions (discrete)
        public int BerthAllocation { get; set; }        // 0-3 (berth selection)
        public int AGVAssignment { get; set; }          // 0-23 (AGV selection)
        public int YardBlockSelection { get; set; }     // 0-15 (yard block selection)
        
        // Secondary decisions (continuous, normalized 0-1)
        public float[] PriorityWeights { get; set; } = new float[3]; // [urgency, efficiency, balance]
        public float ResourceAllocationBias { get; set; }  // Favor speed vs. efficiency
        public float CongestionAvoidance { get; set; }     // Conservative vs. aggressive routing
    }

    public class StepResult
    {
        public SimulationState NewState { get; set; }
        public double Reward { get; set; }
        public bool Done { get; set; }
        public Dictionary<string, object> Info { get; set; } = new();
    }

    public class VesselState
    {
        public string Id { get; set; }
        public double ArrivalTime { get; set; }
        public int ContainerCount { get; set; }
        public bool IsDelayed { get; set; }
        public double WaitingTime { get; set; }
        public int AssignedBerth { get; set; }
    }

    public class BerthState
    {
        public string Id { get; set; }
        public bool IsOccupied { get; set; }
        public string OccupiedByVessel { get; set; }
        public double UtilizationRate { get; set; }
        public int AvailableQCs { get; set; }
    }

    public class AGVState
    {
        public string Id { get; set; }
        public bool IsIdle { get; set; }
        public bool HasContainer { get; set; }
        public double[] Position { get; set; } = new double[2];
        public double DistanceToTarget { get; set; }
    }

    public class QCState
    {
        public string Id { get; set; }
        public bool IsWorking { get; set; }
        public string AssignedBerth { get; set; }
        public double ProductivityRate { get; set; }
    }

    public class YardBlockState
    {
        public string Id { get; set; }
        public int Capacity { get; set; }
        public int CurrentLoad { get; set; }
        public double UtilizationRate { get; set; }
        public int ReservedSlots { get; set; }
    }

    public class PerformanceMetrics
    {
        public double VesselDelayRate { get; set; }
        public int TotalDelayedVessels { get; set; }
        public int TotalVessels { get; set; }
        public double AverageWaitingTime { get; set; }
        public double ThroughputRate { get; set; }
        public double ResourceUtilization { get; set; }
        public DateTime LastUpdated { get; set; }
    }
}